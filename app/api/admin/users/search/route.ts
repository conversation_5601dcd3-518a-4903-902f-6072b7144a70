import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../../lib/mongodb';
import { requireSuperAdminAuth } from '../../../../../lib/api/admin-auth';
import { UserPasscodeModel } from '../../../../../lib/models/user-passcode';
import { SurveyModel } from '../../../../../lib/models/survey';

interface UserSearchResult {
  email: string;
  userName: string;
  passcode: string;
  waveId: string;
  surveyCount: number;
  debugUrl: string;
  usedAt?: Date;
  createdAt: Date;
}

export async function GET(request: NextRequest) {
  try {
    // Verify superadmin authentication
    await requireSuperAdminAuth();

    await dbConnect();

    const { searchParams } = new URL(request.url);
    const searchTerm = searchParams.get('search') || '';
    const limit = parseInt(searchParams.get('limit') || '50');

    // Build the query - always show all users regardless of usage status
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const query: Record<string, any> = {};

    // Don't filter by search term in the database query - we'll do it client-side
    // to allow searching by both email and userName

    // Get user passcodes with search criteria
    const userPasscodes = await UserPasscodeModel.find(query)
      .limit(limit)
      .sort({ createdAt: -1 })
      .select('userEmail passcode waveId usedAt createdAt')
      .lean();

    // For each user passcode, get the userName from surveys and count surveys
    const enhancedResults: UserSearchResult[] = await Promise.all(
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      userPasscodes.map(async (userPasscode: any): Promise<UserSearchResult> => {
        // Get a sample survey to get the userName
        const sampleSurvey = await SurveyModel.findOne({
          userEmail: userPasscode.userEmail,
          waveId: userPasscode.waveId
        }).select('userName').lean() as { userName: string } | null;

        // Count surveys for this user and wave
        const surveyCount = await SurveyModel.countDocuments({
          userEmail: userPasscode.userEmail,
          waveId: userPasscode.waveId
        });

        return {
          email: userPasscode.userEmail,
          userName: sampleSurvey?.userName || 'Unknown',
          passcode: userPasscode.passcode,
          waveId: userPasscode.waveId.toString(),
          surveyCount,
          debugUrl: `/api/auth/debug-user?passcode=${userPasscode.passcode}`,
          usedAt: userPasscode.usedAt,
          createdAt: userPasscode.createdAt
        };
      })
    );

    // Filter results by search term if provided
    let finalResults = enhancedResults;
    if (searchTerm.trim()) {
      const lowerSearchTerm = searchTerm.toLowerCase().trim();
      finalResults = enhancedResults.filter(result =>
        result.email.toLowerCase().includes(lowerSearchTerm) ||
        result.userName.toLowerCase().includes(lowerSearchTerm)
      );
    }
    // If no search term, return all results (already in finalResults)

    return NextResponse.json({
      success: true,
      users: finalResults,
      total: finalResults.length,
      searchTerm: searchTerm.trim()
    });

  } catch (error) {
    console.error('User search error:', error);
    
    if (error instanceof Error && error.message === 'Superadmin access required') {
      return NextResponse.json(
        { error: 'Unauthorized: Superadmin access required' },
        { status: 403 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to search users' },
      { status: 500 }
    );
  }
}
