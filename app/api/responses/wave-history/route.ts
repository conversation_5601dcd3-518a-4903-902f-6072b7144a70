import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();

    const { searchParams } = new URL(request.url);
    const focusType = searchParams.get('focusType') as 'agency' | 'brand' | 'region' | 'wave';
    const focusValue = searchParams.get('focusValue');

    if (!focusType || !focusValue) {
      return NextResponse.json(
        { error: 'focusType and focusValue are required' },
        { status: 400 }
      );
    }

    // Build aggregation pipeline
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const pipeline: any[] = [
      // Join with surveys to get agency/brand/region info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to get wave info and filter out seed data
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter out seed waves
      {
        $match: {
          'wave.status': { $ne: 'seed' }
        }
      }
    ];

    // Add focus filter
    const focusFilterCondition: Record<string, unknown> = {};
    if (focusType === 'agency') {
      focusFilterCondition['survey.agencyName'] = focusValue;
    } else if (focusType === 'brand') {
      focusFilterCondition['survey.brand'] = focusValue;
    } else if (focusType === 'region') {
      focusFilterCondition['survey.country'] = focusValue;
    } else if (focusType === 'wave') {
      // For wave focus, we want to show history across all agencies/brands/regions for that wave
      focusFilterCondition['wave.name'] = focusValue;
    }

    pipeline.push({ $match: focusFilterCondition });

    // Group by wave and calculate average scores
    pipeline.push({
      $group: {
        _id: '$wave.name',
        averageScore: {
          $avg: {
            $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
          }
        },
        // Calculate NPS dynamically from q6Score values
        promoters: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 9] }, { $lte: ['$q6Score', 10] }] },
              1,
              0
            ]
          }
        },
        detractors: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 6] }] },
              1,
              0
            ]
          }
        },
        totalNpsResponses: {
          $sum: {
            $cond: [
              { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 10] }] },
              1,
              0
            ]
          }
        },
        // Use wave creation date for chronological ordering (now set to reporting month)
        waveCreatedAt: { $first: '$wave.createdAt' },
        responseCount: { $sum: 1 }
      }
    });

    // Sort by wave creation date to get chronological order based on reporting months
    pipeline.push({ $sort: { waveCreatedAt: 1 } });

    // Format output
    pipeline.push({
      $project: {
        _id: 0,
        waveName: '$_id',
        averageScore: { $round: ['$averageScore', 1] },
        // Calculate NPS: (% Promoters - % Detractors) * 100
        averageNPS: {
          $cond: [
            { $gt: ['$totalNpsResponses', 0] },
            {
              $round: [
                {
                  $multiply: [
                    {
                      $subtract: [
                        { $divide: ['$promoters', '$totalNpsResponses'] },
                        { $divide: ['$detractors', '$totalNpsResponses'] }
                      ]
                    },
                    100
                  ]
                },
                1
              ]
            },
            null
          ]
        },
        responseCount: 1,
        waveCreatedAt: 1
      }
    });

    const results = await ResponseModel.aggregate(pipeline);

    // If no data found, return empty arrays
    if (results.length === 0) {
      return NextResponse.json({
        waveNames: [],
        scores: [],
        npsScores: [],
        message: `No wave history data available for ${focusValue}.`
      });
    }

    // Extract wave names, scores, and NPS scores for the chart
    const waveNames = results.map(r => r.waveName);
    const scores = results.map(r => r.averageScore);
    const npsScores = results.map(r => r.averageNPS);

    return NextResponse.json({
      waveNames,
      scores,
      npsScores,
      responseCount: results.reduce((sum, r) => sum + r.responseCount, 0)
    });

  } catch (error) {
    console.error('Error fetching wave history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch wave history' },
      { status: 500 }
    );
  }
} 