import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '../../../../lib/mongodb';
import { ResponseModel } from '../../../../lib/models/response';

export async function GET(request: NextRequest) {
  try {
    await dbConnect();
    
    const { searchParams } = new URL(request.url);
    
    // Get the primary filter (first one selected)
    const primaryFilter = searchParams.get('primaryFilter'); // 'agency', 'brand', or 'region'
    const primaryValue = searchParams.get('primaryValue');
    const wave = searchParams.get('wave'); // Optional wave filter
    
    if (!primaryFilter || !primaryValue) {
      return NextResponse.json(
        { error: 'Primary filter and value are required' },
        { status: 400 }
      );
    }
    
    // Build base aggregation pipeline
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const basePipeline: any[] = [
      // Join with surveys to get agency/brand info
      {
        $lookup: {
          from: 'surveys',
          localField: 'surveyId',
          foreignField: '_id',
          as: 'survey'
        }
      },
      { $unwind: '$survey' },
      // Join with waves to filter out seed data
      {
        $lookup: {
          from: 'waves',
          localField: 'survey.waveId',
          foreignField: '_id',
          as: 'wave'
        }
      },
      { $unwind: '$wave' },
      // Filter out seed waves
      {
        $match: {
          'wave.status': { $ne: 'seed' }
        }
      }
    ];
    
    // Add primary filter condition
    const primaryFilterCondition: Record<string, unknown> = {};
    if (primaryFilter === 'agency') {
      primaryFilterCondition['survey.agencyName'] = primaryValue;
    } else if (primaryFilter === 'brand') {
      primaryFilterCondition['survey.brand'] = primaryValue;
    } else if (primaryFilter === 'region') {
      primaryFilterCondition['survey.country'] = primaryValue;
    }
    
    // Add wave filter if provided
    if (wave) {
      const waves = wave.split(',').map(w => w.trim());
      if (waves.length === 1) {
        primaryFilterCondition['wave.name'] = waves[0];
      } else {
        primaryFilterCondition['wave.name'] = { $in: waves };
      }
    }
    
    basePipeline.push({ $match: primaryFilterCondition });
    
    // Determine what dimensions to group by (exclude the primary filter)
    const dimensions = [];
    if (primaryFilter !== 'agency') dimensions.push('agencies');
    if (primaryFilter !== 'brand') dimensions.push('brands');
    if (primaryFilter !== 'region') dimensions.push('regions');
    
    const results: Record<string, unknown[]> = {};
    
    // Get aggregated data for each dimension
    for (const dimension of dimensions) {
      let groupField = '';
      if (dimension === 'agencies') groupField = '$survey.agencyName';
      else if (dimension === 'brands') groupField = '$survey.brand';
      else if (dimension === 'regions') groupField = '$survey.country';
      
      const pipeline = [
        ...basePipeline,
        {
          $group: {
            _id: groupField,
            averageScore: {
              $avg: {
                $avg: ['$q1Score', '$q2Score', '$q3Score', '$q4Score', '$q5Score']
              }
            },
            responseCount: { $sum: 1 },
            // Calculate NPS dynamically from q6Score values
            promoters: {
              $sum: {
                $cond: [
                  { $and: [{ $gte: ['$q6Score', 9] }, { $lte: ['$q6Score', 10] }] },
                  1,
                  0
                ]
              }
            },
            detractors: {
              $sum: {
                $cond: [
                  { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 6] }] },
                  1,
                  0
                ]
              }
            },
            totalNpsResponses: {
              $sum: {
                $cond: [
                  { $and: [{ $gte: ['$q6Score', 1] }, { $lte: ['$q6Score', 10] }] },
                  1,
                  0
                ]
              }
            },
            q1Avg: { $avg: '$q1Score' },
            q2Avg: { $avg: '$q2Score' },
            q3Avg: { $avg: '$q3Score' },
            q4Avg: { $avg: '$q4Score' },
            q5Avg: { $avg: '$q5Score' }
          }
        },
        {
          $project: {
            _id: 0,
            name: '$_id',
            averageScore: { $round: ['$averageScore', 2] },
            responseCount: 1,
            // Calculate NPS: (% Promoters - % Detractors) * 100
            npsScore: {
              $cond: [
                { $gt: ['$totalNpsResponses', 0] },
                {
                  $round: [
                    {
                      $multiply: [
                        {
                          $subtract: [
                            { $divide: ['$promoters', '$totalNpsResponses'] },
                            { $divide: ['$detractors', '$totalNpsResponses'] }
                          ]
                        },
                        100
                      ]
                    },
                    1
                  ]
                },
                null
              ]
            },
            q1Avg: { $round: ['$q1Avg', 2] },
            q2Avg: { $round: ['$q2Avg', 2] },
            q3Avg: { $round: ['$q3Avg', 2] },
            q4Avg: { $round: ['$q4Avg', 2] },
            q5Avg: { $round: ['$q5Avg', 2] }
          }
        },
        { $sort: { averageScore: -1 } }
      ];
      
      const data = await ResponseModel.aggregate(pipeline);
      results[dimension] = data;
    }
    
    return NextResponse.json({
      primaryFilter,
      primaryValue,
      drilldownData: results
    });
  } catch (error) {
    console.error('Error fetching drilldown data:', error);
    return NextResponse.json(
      { error: 'Failed to fetch drilldown data' },
      { status: 500 }
    );
  }
} 